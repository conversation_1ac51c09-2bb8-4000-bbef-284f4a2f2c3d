<!-- Sidebar Component -->
<aside id="sidebar" class="fixed inset-y-0 left-0 z-20 flex h-full w-64 flex-col bg-slate-900 dark:bg-slate-900 border-r border-slate-800 shadow-md transform-gpu transition-all duration-300">
  <!-- Sidebar Header -->
  <div class="flex h-16 items-center border-b border-slate-800 px-4 py-3 sidebar-header">
    <div class="flex items-center space-x-3">
      <div class="flex h-10 w-10 items-center justify-center rounded-md bg-transparent">
        <img src="{{ url_for('static', filename='favicon.svg') }}" alt="Logo" class="h-8 w-8">
      </div>
      <span class="text-lg font-semibold text-white sidebar-text">{{ app_settings.site_name|default('Matrix') }}</span>
    </div>
  </div>

  <!-- Sidebar Content -->
  <div class="flex-1 overflow-y-auto py-4 px-3 sidebar-nav">
    <ul class="space-y-1">
      <!-- User Section removed - using Account dropdown instead -->

      {% if current_user.is_admin %}
      <!-- Admin Divider -->
      <li class="pt-4">
        <div class="mb-2 px-3 text-xs font-semibold uppercase text-slate-500 sidebar-text">ADMIN SECTION</div>
      </li>
      {% endif %}

      {% if current_user.is_admin %}
      <li>
        <a href="{{ url_for('admin.dashboard') }}" class="flex items-center rounded-md p-2 text-slate-400 transition-colors hover:bg-slate-800 hover:text-white my-1 px-3 py-2 {% if active_page == 'dashboard' %}bg-primary/10 text-white{% endif %}">
          <i data-lucide="layout-dashboard" class="h-4 w-4 flex-shrink-0 mr-3 sidebar-icon"></i>
          <span class="text-sm sidebar-text">Dashboard</span>
        </a>
      </li>

      <!-- Admin Section -->
      <li class="pt-4">
        <div class="mb-2 px-3 text-xs font-semibold uppercase text-slate-500 sidebar-text">ADMINISTRATION</div>
      </li>

      <!-- User Management Submenu -->
      <li class="has-submenu">
        <a href="#" class="submenu-toggle flex items-center justify-between rounded-md p-2 text-slate-400 transition-colors hover:bg-slate-800 hover:text-white my-1 px-3 py-2" data-submenu-id="user-management">
          <div class="flex items-center">
            <i data-lucide="users" class="h-4 w-4 flex-shrink-0 mr-3 sidebar-icon"></i>
            <span class="text-sm sidebar-text">User Management</span>
          </div>
          <span class="chevron-wrapper">
            <i data-lucide="chevron-down" class="h-4 w-4 sidebar-text"></i>
          </span>
        </a>
        <ul class="submenu-content">
          <li>
            <div class="pl-2 pb-1 ml-1">
              <a href="{{ url_for('admin.users') }}" class="submenu-item flex items-center rounded-md p-1 text-slate-400 transition-colors hover:bg-slate-800 hover:text-white {% if active_page == 'users' %}bg-primary/10 text-white{% endif %}">
                <span class="text-sm sidebar-text">Users</span>
              </a>
            </div>
          </li>
          <li>
            <div class="pl-2 pb-1 ml-1">
              <a href="{{ url_for('admin.employee_details') }}" class="submenu-item flex items-center rounded-md p-1 text-slate-400 transition-colors hover:bg-slate-800 hover:text-white {% if active_page == 'employee_details' %}bg-primary/10 text-white{% endif %}">
                <span class="text-sm sidebar-text">Employee Details</span>
              </a>
            </div>
          </li>
          <li>
            <div class="pl-2 pb-1 ml-1">
              <a href="{{ url_for('teams.index') }}" class="submenu-item flex items-center rounded-md p-1 text-slate-400 transition-colors hover:bg-slate-800 hover:text-white {% if active_page == 'teams' %}bg-primary/10 text-white{% endif %}">
                <span class="text-sm sidebar-text">Teams</span>
              </a>
            </div>
          </li>
        </ul>
      </li>

      <!-- Business Submenu -->
      <li class="has-submenu">
        <a href="#" class="submenu-toggle flex items-center justify-between rounded-md p-2 text-slate-400 transition-colors hover:bg-slate-800 hover:text-white my-1 px-3 py-2" data-submenu-id="business">
          <div class="flex items-center">
            <i data-lucide="building" class="h-4 w-4 flex-shrink-0 mr-3 sidebar-icon"></i>
            <span class="text-sm sidebar-text">Business</span>
          </div>
          <span class="chevron-wrapper">
            <i data-lucide="chevron-down" class="h-4 w-4 sidebar-text"></i>
          </span>
        </a>
        <ul class="submenu-content">
          <li>
            <div class="pl-2 pb-1 ml-1">
              <a href="{{ url_for('admin.business_units') }}" class="submenu-item flex items-center rounded-md p-1 text-slate-400 transition-colors hover:bg-slate-800 hover:text-white {% if active_page == 'business_units' %}bg-primary/10 text-white{% endif %}">
                <span class="text-sm sidebar-text">Units</span>
              </a>
            </div>
          </li>
          <li>
            <div class="pl-2 pb-1 ml-1">
              <a href="{{ url_for('admin.business_segments') }}" class="submenu-item flex items-center rounded-md p-1 text-slate-400 transition-colors hover:bg-slate-800 hover:text-white {% if active_page == 'business_segments' %}bg-primary/10 text-white{% endif %}">
                <span class="text-sm sidebar-text">Segments</span>
              </a>
            </div>
          </li>
        </ul>
      </li>

      <!-- Attendance Submenu -->
      <li class="has-submenu">
        <a href="#" class="submenu-toggle flex items-center justify-between rounded-md p-2 text-slate-400 transition-colors hover:bg-slate-800 hover:text-white my-1 px-3 py-2" data-submenu-id="attendance-management">
          <div class="flex items-center">
            <i data-lucide="calendar" class="h-4 w-4 flex-shrink-0 mr-3 sidebar-icon"></i> {# Icon for Attendance #}
            <span class="text-sm sidebar-text">Attendance</span>
          </div>
          <span class="chevron-wrapper">
            <i data-lucide="chevron-down" class="h-4 w-4 sidebar-text"></i>
          </span>
        </a>
        <ul class="submenu-content">
          <li>
            <div class="pl-2 pb-1 ml-1">
              <a href="{{ url_for('admin_attendance.list_attendance_types') }}" class="submenu-item flex items-center rounded-md p-1 text-slate-400 transition-colors hover:bg-slate-800 hover:text-white {% if active_page == 'attendance_types' %}bg-primary/10 text-white{% endif %}">
                <span class="text-sm sidebar-text">Attendance Types</span>
              </a>
            </div>
          </li>
          <li>
            <div class="pl-2 pb-1 ml-1">
              <a href="{{ url_for('admin.holidays') }}" class="submenu-item flex items-center rounded-md p-1 text-slate-400 transition-colors hover:bg-slate-800 hover:text-white {% if active_page == 'holidays' %}bg-primary/10 text-white{% endif %}">
                <span class="text-sm sidebar-text">Holidays</span>
              </a>
            </div>
          </li>
          <li>
            <div class="pl-2 pb-1 ml-1">
              <a href="{{ url_for('admin.holiday_calendar') }}" class="submenu-item flex items-center rounded-md p-1 text-slate-400 transition-colors hover:bg-slate-800 hover:text-white {% if active_page == 'holiday_calendar' %}bg-primary/10 text-white{% endif %}">
                <span class="text-sm sidebar-text">Holiday Calendar</span>
              </a>
            </div>
          </li>
        </ul>
      </li>

      <!-- Analytics Submenu -->
      <li class="has-submenu">
        <a href="#" class="submenu-toggle flex items-center justify-between rounded-md p-2 text-slate-400 transition-colors hover:bg-slate-800 hover:text-white my-1 px-3 py-2" data-submenu-id="analytics">
          <div class="flex items-center">
            <i data-lucide="bar-chart" class="h-4 w-4 flex-shrink-0 mr-3 sidebar-icon"></i>
            <span class="text-sm sidebar-text">Analytics</span>
          </div>
          <span class="chevron-wrapper">
            <i data-lucide="chevron-down" class="h-4 w-4 sidebar-text"></i>
          </span>
        </a>
        <ul class="submenu-content">
          <li>
            <div class="pl-2 pb-1 ml-1">
              <a href="{{ url_for('admin.analytics') }}" class="submenu-item flex items-center rounded-md p-1 text-slate-400 transition-colors hover:bg-slate-800 hover:text-white {% if active_page == 'analytics' %}bg-primary/10 text-white{% endif %}">
                <span class="text-sm sidebar-text">User Analytics</span>
              </a>
            </div>
          </li>

        </ul>
      </li>

      <!-- Activity Logs -->
      <li>
        <a href="{{ url_for('admin.activities') }}" class="flex items-center rounded-md p-2 text-slate-400 transition-colors hover:bg-slate-800 hover:text-white my-1 px-3 py-2 {% if active_page == 'admin_activities' %}bg-primary/10 text-white{% endif %}">
          <i data-lucide="activity" class="h-4 w-4 flex-shrink-0 mr-3 sidebar-icon"></i>
          <span class="text-sm sidebar-text">Activity Logs</span>
        </a>
      </li>

      <!-- System Submenu -->
      <li class="has-submenu">
        <a href="#" class="submenu-toggle flex items-center justify-between rounded-md p-2 text-slate-400 transition-colors hover:bg-slate-800 hover:text-white my-1 px-3 py-2" data-submenu-id="system">
          <div class="flex items-center">
            <i data-lucide="settings" class="h-4 w-4 flex-shrink-0 mr-3 sidebar-icon"></i>
            <span class="text-sm sidebar-text">System</span>
          </div>
          <span class="chevron-wrapper">
            <i data-lucide="chevron-down" class="h-4 w-4 sidebar-text"></i>
          </span>
        </a>
        <ul class="submenu-content">
          <li>
            <div class="pl-2 pb-1 ml-1">
              <a href="{{ url_for('admin.settings') }}" class="submenu-item flex items-center rounded-md p-1 text-slate-400 transition-colors hover:bg-slate-800 hover:text-white {% if active_page == 'settings' %}bg-primary/10 text-white{% endif %}">
                <span class="text-sm sidebar-text">Settings</span>
              </a>
            </div>
          </li>
          <li>
            <div class="pl-2 pb-1 ml-1">
              <a href="{{ url_for('admin.maintenance') }}" class="submenu-item flex items-center rounded-md p-1 text-slate-400 transition-colors hover:bg-slate-800 hover:text-white {% if active_page == 'maintenance' %}bg-primary/10 text-white{% endif %}">
                <span class="text-sm sidebar-text">Maintenance</span>
              </a>
            </div>
          </li>
          <li>
            <div class="pl-2 pb-1 ml-1">
              <a href="{{ url_for('admin.icon_repository') }}" class="submenu-item flex items-center rounded-md p-1 text-slate-400 transition-colors hover:bg-slate-800 hover:text-white {% if active_page == 'icons' %}bg-primary/10 text-white{% endif %}">
                <span class="text-sm sidebar-text">Icon Repository</span>
              </a>
            </div>
          </li>
        </ul>
      </li>

      <!-- Main Platform -->
      <li class="pt-4">
        <div class="mb-2 px-3 text-xs font-semibold uppercase text-slate-500 sidebar-text">PLATFORM</div>
      </li>

      <!-- Reports Submenu -->
      <li class="has-submenu">
        <a href="#" class="submenu-toggle flex items-center justify-between rounded-md p-2 text-slate-400 transition-colors hover:bg-slate-800 hover:text-white my-1 px-3 py-2" data-submenu-id="reports">
          <div class="flex items-center">
            <i data-lucide="message-square" class="h-4 w-4 flex-shrink-0 mr-3 sidebar-icon"></i>
            <span class="text-sm sidebar-text">Reports</span>
          </div>
          <span class="chevron-wrapper">
            <i data-lucide="chevron-down" class="h-4 w-4 sidebar-text"></i>
          </span>
        </a>
        <ul class="submenu-content">
          <li>
            <div class="pl-2 pb-1 ml-1">
              <a href="#" class="submenu-item flex items-center justify-between rounded-md p-1 text-slate-400 transition-colors hover:bg-slate-800 hover:text-white {% if active_page == 'attendances' %}bg-primary/10 text-white{% endif %}">
                <span class="text-sm sidebar-text">Attendance</span>
                <!-- <span class="text-xs bg-primary/20 text-primary px-2 py-0.5 rounded-full sidebar-badge">
                  {{ current_user.received_messages|selectattr('is_read', 'equalto', false)|list|length }}
                </span> -->
              </a>
            </div>
          </li>
        </ul>
      </li>
      {% else %}
      <!-- User Dashboard -->
      <li>
        <a href="{{ url_for('main.user_dashboard') }}" class="flex items-center rounded-md p-2 text-slate-400 transition-colors hover:bg-slate-800 hover:text-white my-1 px-3 py-2 {% if active_page == 'user_dashboard' %}bg-primary/10 text-primary{% endif %}">
          <i data-lucide="layout-dashboard" class="h-4 w-4 flex-shrink-0 mr-3 sidebar-icon"></i>
          <span class="text-sm sidebar-text">Dashboard</span>
        </a>
      </li>

      <!-- User Section -->
      <li class="pt-4">
        <div class="mb-2 px-3 text-xs font-semibold uppercase text-slate-500 sidebar-text">MY INFORMATION</div>
      </li>

      <!-- Personal Info Submenu -->
      <li class="has-submenu">
        <a href="#" class="submenu-toggle flex items-center justify-between rounded-md p-2 text-slate-400 transition-colors hover:bg-slate-800 hover:text-white my-1 px-3 py-2" data-submenu-id="personal-info">
          <div class="flex items-center">
            <i data-lucide="user" class="h-4 w-4 flex-shrink-0 mr-3 sidebar-icon"></i>
            <span class="text-sm sidebar-text">Personal Info</span>
          </div>
          <span class="chevron-wrapper">
            <i data-lucide="chevron-down" class="h-4 w-4 sidebar-text"></i>
          </span>
        </a>
        <ul class="submenu-content">
          <li>
            <div class="pl-2 ml-1">
              <a href="{{ url_for('main.view_my_details') }}" class="submenu-item flex items-center rounded-md p-2 text-slate-400 transition-colors hover:bg-slate-800 hover:text-white {% if active_page == 'my_details' %}bg-primary/10 text-primary{% endif %}">
                <span class="text-sm sidebar-text">My Details</span>
              </a>
            </div>
          </li>
          <li>
            <div class="pl-2 ml-1">
              <a href="{{ url_for('main.messages') }}" class="submenu-item flex items-center justify-between rounded-md p-2 text-slate-400 transition-colors hover:bg-slate-800 hover:text-white {% if active_page == 'messages' %}bg-primary/10 text-primary{% endif %}">
                <span class="text-sm sidebar-text">Messages</span>
                <span class="text-xs bg-primary/20 text-primary px-2 py-0.5 rounded-full sidebar-badge">
                  {{ current_user.received_messages|selectattr('is_read', 'equalto', false)|list|length }}
                </span>
              </a>
            </div>
          </li>
        </ul>
      </li>
      {% endif %}
    </ul>
  </div>

  <!-- Sidebar Footer -->
  <div class="mt-auto border-t border-slate-800 p-3">
    <button id="user-menu-button" class="flex w-full items-center justify-between rounded-md p-2 text-slate-400 transition-colors hover:bg-slate-800 hover:text-white">
      <div class="flex items-center space-x-3">
        <div class="flex h-8 w-8 items-center justify-center rounded-full bg-slate-700 text-slate-300">
          <i data-lucide="user" class="h-4 w-4"></i>
        </div>
        <div class="sidebar-text">
          <p class="text-sm font-medium text-white">{{ current_user.name }}</p>
          <p class="text-xs text-slate-400">{{ current_user.email }}</p>
        </div>
      </div>
      <span class="chevron-wrapper">
        <i data-lucide="chevrons-up-down" class="h-4 w-4 sidebar-text" id="user-menu-chevron"></i>
      </span>
    </button>

    <!-- User dropdown menu -->
    <div id="user-dropdown" class="hidden fixed z-[100] min-w-[240px] rounded-md border border-slate-800 bg-slate-900 shadow-md overflow-hidden animate-in fade-in-0 duration-150 origin-top-right">
      <div class="p-3 border-b border-slate-800">
        <p class="text-sm font-medium text-white">{{ current_user.name }}</p>
        <p class="text-xs text-slate-400">{{ current_user.email }}</p>
      </div>
      <div class="py-1">
        <a href="{{ url_for('auth.profile') }}" class="flex text-left items-center px-3 py-2 text-sm text-slate-300 hover:bg-slate-800 hover:text-white transition-colors">
          <i data-lucide="user" class="h-4 w-4 mr-2 flex-shrink-0"></i>
          <span>Account</span>
        </a>
      </div>
      <div class="border-t border-slate-800 py-1">
        <a href="{{ url_for('auth.logout') }}" class="flex text-left items-center px-3 py-2 text-sm text-slate-300 hover:bg-slate-800 hover:text-white transition-colors">
          <i data-lucide="log-out" class="h-4 w-4 mr-2 flex-shrink-0"></i>
          <span>Log out</span>
        </a>
      </div>
    </div>
  </div>
</aside>

<!-- Mobile Sidebar Overlay -->
<div id="sidebar-overlay" class="hidden fixed inset-0 bg-black/50 backdrop-blur-sm z-10 lg:hidden transition-opacity duration-300 opacity-0"></div>
