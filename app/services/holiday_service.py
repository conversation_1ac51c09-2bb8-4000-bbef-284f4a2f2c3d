"""
Holiday service layer for business logic related to holidays.
"""
from datetime import date, timedelta
from typing import List, Optional, Dict, Any

from app import db
from app.models.attendance import Holiday, AttendanceRecord
from app.models.employee import EmployeeDetail


class HolidayService:
    """Service class for holiday-related business logic."""

    @staticmethod
    def is_holiday(check_date: date, region_code: str) -> bool:
        """
        Check if a specific date is a holiday for the given region.
        
        Args:
            check_date: The date to check
            region_code: The region code (e.g., 'US', 'PH', 'GLOBAL')
            
        Returns:
            True if the date is a holiday, False otherwise
        """
        return Holiday.is_holiday(check_date, region_code)

    @staticmethod
    def get_holidays_by_region(region_code: str, start_date: Optional[date] = None, 
                              end_date: Optional[date] = None) -> List[Holiday]:
        """
        Get holidays for a specific region within date range.
        
        Args:
            region_code: The region code (e.g., 'US', 'PH', 'GLOBAL')
            start_date: Optional start date filter
            end_date: Optional end date filter
            
        Returns:
            List of Holiday objects
        """
        return Holiday.get_by_region(region_code, start_date, end_date)

    @staticmethod
    def get_holidays_by_date_range(start_date: date, end_date: date, 
                                  region_code: Optional[str] = None) -> List[Holiday]:
        """
        Get holidays within a date range, optionally filtered by region.
        
        Args:
            start_date: Start date of the range
            end_date: End date of the range
            region_code: Optional region code filter
            
        Returns:
            List of Holiday objects
        """
        return Holiday.get_by_date_range(start_date, end_date, region_code)

    @staticmethod
    def get_upcoming_holidays(region_code: str, days: int = 30) -> List[Holiday]:
        """
        Get upcoming holidays for a region within the next N days.
        
        Args:
            region_code: The region code (e.g., 'US', 'PH', 'GLOBAL')
            days: Number of days to look ahead (default: 30)
            
        Returns:
            List of Holiday objects
        """
        return Holiday.get_upcoming_holidays(region_code, days)

    @staticmethod
    def check_holiday_conflicts(employee_id: int, check_date: date) -> Dict[str, Any]:
        """
        Check if an employee has attendance conflicts with holidays.
        
        Args:
            employee_id: The employee ID
            check_date: The date to check
            
        Returns:
            Dictionary with conflict information
        """
        # Get employee details to determine region
        employee = EmployeeDetail.query.get(employee_id)
        if not employee:
            return {
                'has_conflict': False,
                'error': 'Employee not found'
            }

        # For now, assume all employees are in PH region
        # This can be enhanced later to get region from employee profile
        region_code = 'PH'
        
        # Check if the date is a holiday
        is_holiday_date = HolidayService.is_holiday(check_date, region_code)
        
        if not is_holiday_date:
            return {
                'has_conflict': False,
                'is_holiday': False
            }

        # Get the holiday details
        holiday = Holiday.query.filter(
            Holiday.date == check_date,
            db.or_(Holiday.region_code == region_code, Holiday.region_code == 'GLOBAL')
        ).first()

        # Check if employee has any attendance records on this holiday
        attendance_records = AttendanceRecord.query.filter(
            AttendanceRecord.employee_detail_id == employee_id,
            AttendanceRecord.date == check_date
        ).all()

        return {
            'has_conflict': len(attendance_records) > 0,
            'is_holiday': True,
            'holiday': holiday.to_dict() if holiday else None,
            'attendance_records': [record.id for record in attendance_records],
            'region_code': region_code
        }

    @staticmethod
    def get_holiday_calendar_data(region_code: str, year: int, month: Optional[int] = None) -> Dict[str, Any]:
        """
        Get holiday calendar data for a specific region, year, and optionally month.
        
        Args:
            region_code: The region code (e.g., 'US', 'PH', 'GLOBAL')
            year: The year to get holidays for
            month: Optional month (1-12) to filter by
            
        Returns:
            Dictionary with calendar data
        """
        if month:
            # Get holidays for specific month
            start_date = date(year, month, 1)
            if month == 12:
                end_date = date(year + 1, 1, 1) - timedelta(days=1)
            else:
                end_date = date(year, month + 1, 1) - timedelta(days=1)
        else:
            # Get holidays for entire year
            start_date = date(year, 1, 1)
            end_date = date(year, 12, 31)

        holidays = HolidayService.get_holidays_by_region(region_code, start_date, end_date)
        
        # Format holidays for calendar display
        calendar_data = {}
        for holiday in holidays:
            date_str = holiday.date.isoformat()
            if date_str not in calendar_data:
                calendar_data[date_str] = []
            
            calendar_data[date_str].append({
                'id': holiday.id,
                'name': holiday.name,
                'description': holiday.description,
                'region_code': holiday.region_code
            })

        return {
            'year': year,
            'month': month,
            'region_code': region_code,
            'holidays': calendar_data,
            'total_holidays': len(holidays)
        }

    @staticmethod
    def validate_holiday_data(name: str, check_date: date, region_code: str, 
                             holiday_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Validate holiday data for creation or update.
        
        Args:
            name: Holiday name
            check_date: Holiday date
            region_code: Region code
            holiday_id: Optional holiday ID for updates
            
        Returns:
            Dictionary with validation results
        """
        errors = []
        
        # Check if holiday already exists for this date and region
        existing_holiday = Holiday.query.filter(
            Holiday.date == check_date,
            Holiday.region_code == region_code
        ).first()
        
        if existing_holiday and (not holiday_id or existing_holiday.id != holiday_id):
            errors.append(f"A holiday already exists for {check_date} in region {region_code}")
        
        # Validate region code
        valid_regions = ['US', 'PH', 'GLOBAL']
        if region_code not in valid_regions:
            errors.append(f"Invalid region code. Must be one of: {', '.join(valid_regions)}")
        
        # Validate name
        if not name or len(name.strip()) < 2:
            errors.append("Holiday name must be at least 2 characters long")
        
        # Check if date is in the past (warning, not error)
        warnings = []
        if check_date < date.today():
            warnings.append("Holiday date is in the past")
        
        return {
            'is_valid': len(errors) == 0,
            'errors': errors,
            'warnings': warnings
        }
