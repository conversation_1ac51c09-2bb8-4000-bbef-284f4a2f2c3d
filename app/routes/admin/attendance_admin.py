from flask import Blueprint, render_template, request, flash, redirect, url_for, jsonify, current_app
from flask_login import login_required
from app import db
from app.models.attendance import AttendanceType
from app.forms.admin_attendance import AttendanceTypeForm
from app.utils.decorators import admin_required
from app.utils.ajax_helpers import ajax_response
from app.utils.pagination import paginate_query

admin_attendance_bp = Blueprint('admin_attendance', __name__, url_prefix='/admin/attendance')

@admin_attendance_bp.route('/types', methods=['GET'])
@login_required
@admin_required
def list_attendance_types():
    """Displays a list of all attendance types."""
    query = AttendanceType.query.order_by(AttendanceType.name.asc())
    per_page = current_app.config.get('PAGINATION_PER_PAGE_ADMIN', 20)
    attendance_types, pagination = paginate_query(query, per_page=per_page)

    return render_template('admin/attendance/types_index.html',
                           attendance_types=attendance_types,
                           title="Manage Attendance Types",
                           pagination=pagination,
                           active_page="attendance_types")

@admin_attendance_bp.route('/types/form/new', methods=['GET'])
@login_required
@admin_required
def get_attendance_type_form_create():
   """Serves the HTML for the create attendance type form (for drawer)."""
   form = AttendanceTypeForm()
   return render_template('admin/attendance/partials/type_form.html',
                          form=form,
                          action_url=url_for('admin_attendance.create_attendance_type'),
                          form_title="Add New Attendance Type")

@admin_attendance_bp.route('/types/form/edit/<int:type_id>', methods=['GET'])
@login_required
@admin_required
def get_attendance_type_form_edit(type_id):
   """Serves the HTML for the edit attendance type form (for drawer)."""
   att_type = AttendanceType.query.get_or_404(type_id)
   form = AttendanceTypeForm(obj=att_type, original_code=att_type.code)
   return render_template('admin/attendance/partials/type_form.html',
                          form=form,
                          action_url=url_for('admin_attendance.update_attendance_type', type_id=type_id),
                          form_title="Edit Attendance Type")

@admin_attendance_bp.route('/types/create', methods=['POST'])
@login_required
@admin_required
@ajax_response(success_redirect='admin_attendance.list_attendance_types')
def create_attendance_type():
   """Handles creation of a new attendance type."""
   form = AttendanceTypeForm()

   if form.validate_on_submit():
       new_type = AttendanceType()
       new_type.code = form.code.data
       new_type.name = form.name.data
       new_type.description = form.description.data
       new_type.requires_approval = form.requires_approval.data
       new_type.is_full_day = form.is_full_day.data
       new_type.color_code = form.color_code.data if form.color_code.data else None

       db.session.add(new_type)
       try:
           db.session.commit()
           return {
               'success': True,
               'message': 'Attendance Type created successfully!',
               'redirect_url': url_for('admin_attendance.list_attendance_types')
           }
       except Exception as e:
           db.session.rollback()
           return {
               'success': False,
               'message': f'Error creating attendance type: {str(e)}',
               'errors': form.errors
           }

   # Form validation failed
   error_messages = {field: errors for field, errors in form.errors.items()}
   return {
       'success': False,
       'message': 'Please correct the errors below.',
       'errors': error_messages
   }

@admin_attendance_bp.route('/types/edit/<int:type_id>', methods=['POST'])
@login_required
@admin_required
@ajax_response(success_redirect='admin_attendance.list_attendance_types')
def update_attendance_type(type_id):
   """Handles updating an existing attendance type."""
   att_type = AttendanceType.query.get_or_404(type_id)
   form = AttendanceTypeForm(obj=att_type, original_code=att_type.code)

   # Temporarily set the form's code data to the current type's code if it wasn't submitted
   # This is to ensure validate_on_submit() doesn't fail if code is not part of the POST data
   # (e.g. if it's a disabled field in the form but still needed for validation context)
   if 'code' not in request.form:
       form.code.data = att_type.code

   if form.validate_on_submit():
       att_type.code = form.code.data
       att_type.name = form.name.data
       att_type.description = form.description.data
       att_type.requires_approval = form.requires_approval.data
       att_type.is_full_day = form.is_full_day.data
       att_type.color_code = form.color_code.data if form.color_code.data else None

       try:
           db.session.commit()
           return {
               'success': True,
               'message': 'Attendance Type updated successfully!'
           }
       except Exception as e:
           db.session.rollback()
           return {
               'success': False,
               'message': f'Error updating attendance type: {str(e)}',
               'errors': form.errors
           }

   # Form validation failed
   error_messages = {field: errors for field, errors in form.errors.items()}
   return {
       'success': False,
       'message': 'Please correct the errors below.',
       'errors': error_messages
   }

@admin_attendance_bp.route('/types/delete/<int:type_id>', methods=['POST'])
@login_required
@admin_required
@ajax_response(success_redirect='admin_attendance.list_attendance_types')
def delete_attendance_type(type_id):
   """Handles deleting an attendance type."""
   att_type = AttendanceType.query.get_or_404(type_id)

   try:
       db.session.delete(att_type)
       db.session.commit()
       return {
           'success': True,
           'message': f'Attendance Type "{att_type.name}" deleted successfully!'
       }
   except Exception as e:
       db.session.rollback()
       return {
           'success': False,
           'message': f'Error deleting attendance type "{att_type.name}": {str(e)}. It might be in use.'
       }
